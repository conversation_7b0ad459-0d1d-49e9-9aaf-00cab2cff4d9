"""
测试RPyC连接的管理命令
"""
from django.core.management.base import BaseCommand
from apps.infection.rpyc_service import RPyCDeviceService


class Command(BaseCommand):
    help = '测试RPyC连接功能'

    def add_arguments(self, parser):
        parser.add_argument(
            '--device-id',
            type=str,
            help='设备ID'
        )
        parser.add_argument(
            '--host',
            type=str,
            help='设备IP地址'
        )
        parser.add_argument(
            '--port',
            type=int,
            default=18861,
            help='RPyC端口号'
        )
        parser.add_argument(
            '--command',
            type=str,
            choices=['register', 'status', 'online', 'execute'],
            help='要执行的操作'
        )
        parser.add_argument(
            '--cmd',
            type=str,
            help='要执行的命令（当command=execute时使用）'
        )

    def handle(self, *args, **options):
        device_id = options.get('device_id')
        host = options.get('host')
        port = options.get('port')
        command = options.get('command')
        cmd = options.get('cmd')

        # 初始化服务
        RPyCDeviceService.initialize()

        try:
            if command == 'register':
                if not device_id or not host:
                    self.stdout.write(
                        self.style.ERROR('注册设备需要提供 --device-id 和 --host 参数')
                    )
                    return

                success = RPyCDeviceService.register_device_connection(device_id, host, port)
                if success:
                    self.stdout.write(
                        self.style.SUCCESS(f'设备 {device_id} 注册成功')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'设备 {device_id} 注册失败')
                    )

            elif command == 'status':
                if not device_id:
                    self.stdout.write(
                        self.style.ERROR('查询设备状态需要提供 --device-id 参数')
                    )
                    return

                status = RPyCDeviceService.get_device_status(device_id)
                self.stdout.write(f'设备状态: {status}')

            elif command == 'online':
                online_devices = RPyCDeviceService.get_online_devices()
                self.stdout.write(f'在线设备: {online_devices}')

            elif command == 'execute':
                if not device_id or not cmd:
                    self.stdout.write(
                        self.style.ERROR('执行命令需要提供 --device-id 和 --cmd 参数')
                    )
                    return

                result = RPyCDeviceService.execute_device_command(device_id, cmd)
                self.stdout.write(f'命令执行结果: {result}')

            else:
                self.stdout.write(
                    self.style.ERROR('请指定要执行的操作: register, status, online, execute')
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'操作失败: {e}')
            )
        finally:
            # 清理资源
            RPyCDeviceService.shutdown()
