from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class InfectionConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.infection'

    def ready(self):
        # 初始化RPyC服务
        try:
            from .rpyc_service import RPyCDeviceService
            RPyCDeviceService.initialize()
            logger.info("RPyC设备服务已初始化")
        except Exception as e:
            logger.error(f"RPyC服务初始化失败: {e}")

        # 注册应用关闭时的清理函数
        import atexit
        from .rpyc_service import RPyCDeviceService
        atexit.register(RPyCDeviceService.shutdown)
